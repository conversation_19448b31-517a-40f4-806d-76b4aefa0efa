"""Authentication service for handling user authentication and token management."""

from datetime import <PERSON><PERSON><PERSON>
from typing import <PERSON><PERSON>, <PERSON><PERSON>

from sqlalchemy.orm import Session

from app.core.security import (
    create_access_token,
    create_refresh_token,
    get_password_hash,
    verify_password,
    verify_token
)
from app.core.config import settings
from app.models.user import User
from app.schemas.auth import LoginRequest, RefreshTokenRequest
from app.schemas.user import UserCreate


class AuthService:
    """Service for handling authentication operations."""
    
    @staticmethod
    def create_user(db: Session, user_create: UserCreate) -> User:
        """Create a new user with hashed password."""
        # Check if user already exists
        if db.query(User).filter(User.email == user_create.email).first():
            raise ValueError("User with this email already exists")
        
        # Create new user
        hashed_password = get_password_hash(user_create.password)
        db_user = User(
            email=user_create.email,
            name=user_create.name,
            password_hash=hashed_password,
            role=user_create.role
        )
        
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        return db_user
    
    @staticmethod
    def authenticate_user(db: Session, login_request: LoginRequest) -> Optional[User]:
        """Authenticate user with email and password."""
        user = db.query(User).filter(User.email == login_request.email).first()
        if not user:
            return None
        if not verify_password(login_request.password, user.password_hash):
            return None
        return user
    
    @staticmethod
    def create_tokens(user: User) -> Tuple[str, str]:
        """Create access and refresh tokens for user."""
        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        refresh_token_expires = timedelta(days=settings.refresh_token_expire_days)
        
        access_token = create_access_token(
            data={"sub": user.email, "user_id": user.id, "role": user.role},
            expires_delta=access_token_expires
        )
        refresh_token = create_refresh_token(
            data={"sub": user.email, "user_id": user.id},
            expires_delta=refresh_token_expires
        )
        
        return access_token, refresh_token
    
    @staticmethod
    def refresh_access_token(db: Session, refresh_token: str) -> Optional[str]:
        """Create new access token from refresh token."""
        payload = verify_token(refresh_token, token_type="refresh")
        if not payload:
            return None
        
        user_id = payload.get("user_id")
        if not user_id:
            return None
        
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            return None
        
        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        access_token = create_access_token(
            data={"sub": user.email, "user_id": user.id, "role": user.role},
            expires_delta=access_token_expires
        )
        
        return access_token