<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task & Resource Tracker</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.6.2/axios.min.js"></script>
    <style>
        * {
            transition: all 0.3s ease;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            font-weight: 600;
            padding: 0.5rem 1.5rem;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            border: none;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.15);
        }
        
        .btn-secondary {
            background-color: #f3f4f6;
            color: #1f2937;
            font-weight: 600;
            padding: 0.5rem 1.5rem;
            border-radius: 0.5rem;
            cursor: pointer;
            border: none;
        }
        
        .btn-secondary:hover {
            background-color: #e5e7eb;
        }
        
        .btn-danger {
            background-color: #ef4444;
            color: white;
            font-weight: 600;
            padding: 0.5rem 1.5rem;
            border-radius: 0.5rem;
            cursor: pointer;
            border: none;
        }
        
        .btn-danger:hover {
            background-color: #dc2626;
        }
        
        .input-field {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            font-size: 1rem;
        }
        
        .input-field:focus {
            outline: none;
            ring: 2px;
            ring-color: #3b82f6;
            border-color: transparent;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .sidebar-nav-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            color: #4b5563;
            cursor: pointer;
            font-weight: 500;
            background-color: transparent;
        }
        
        .sidebar-nav-item:hover {
            background-color: #eff6ff;
            color: #2563eb;
        }
        
        .sidebar-nav-item.active {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
        }
        
        .stat-card {
            background-color: white;
            border-radius: 1rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            border: 1px solid #f3f4f6;
        }
        
        .stat-card:hover {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .stat-card-gradient-blue {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            border-radius: 1rem;
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2);
            padding: 1.5rem;
            color: white;
        }
        
        .stat-card-gradient-green {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border-radius: 1rem;
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2);
            padding: 1.5rem;
            color: white;
        }
        
        .stat-card-gradient-yellow {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            border-radius: 1rem;
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2);
            padding: 1.5rem;
            color: white;
        }
        
        .stat-card-gradient-purple {
            background: linear-gradient(135deg, #a855f7 0%, #9333ea 100%);
            border-radius: 1rem;
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2);
            padding: 1.5rem;
            color: white;
        }
        
        .stat-card-gradient-indigo {
            background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
            border-radius: 1rem;
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2);
            padding: 1.5rem;
            color: white;
        }
        
        .stat-card-gradient-red {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            border-radius: 1rem;
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2);
            padding: 1.5rem;
            color: white;
        }
        
        .stat-card-gradient-cyan {
            background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
            border-radius: 1rem;
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2);
            padding: 1.5rem;
            color: white;
        }
        
        .task-card {
            background-color: white;
            border-radius: 1rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1.25rem;
            border-left: 4px solid;
        }
        
        .task-card:hover {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .task-card-priority-high {
            border-left-color: #ef4444;
        }
        
        .task-card-priority-medium {
            border-left-color: #f59e0b;
        }
        
        .task-card-priority-low {
            border-left-color: #10b981;
        }
        
        .project-card {
            background-color: white;
            border-radius: 1rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
        }
        
        .project-card:hover {
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.15);
        }
        
        .badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .badge-success {
            background-color: #dcfce7;
            color: #166534;
        }
        
        .badge-warning {
            background-color: #fef3c7;
            color: #92400e;
        }
        
        .badge-danger {
            background-color: #fee2e2;
            color: #991b1b;
        }
        
        .badge-primary {
            background-color: #dbeafe;
            color: #1e40af;
        }
        
        .badge-info {
            background-color: #e0e7ff;
            color: #3730a3;
        }
        
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            align-items: center;
            justify-content: center;
            z-index: 50;
        }
        
        .modal-overlay.hidden {
            display: none !important;
        }
        
        .modal-content {
            background-color: white;
            border-radius: 1rem;
            box-shadow: 0 20px 25px rgba(0, 0, 0, 0.2);
            padding: 2rem;
            max-width: 28rem;
            width: 100%;
        }
        
        .progress-bar {
            height: 0.5rem;
            background-color: #e5e7eb;
            border-radius: 9999px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981 0%, #059669 100%);
            border-radius: 9999px;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        
        .section-title {
            font-size: 1.875rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .section-subtitle {
            color: #6b7280;
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Auth Page -->
    <div id="authPage" class="min-h-screen">
        <!-- Auth Header -->
        <div class="gradient-bg py-12 px-4">
            <div class="container mx-auto text-center text-white">
                <div class="text-5xl mb-4">📋</div>
                <h1 class="text-4xl font-bold mb-2">Task & Resource Tracker</h1>
                <p class="text-blue-100 text-lg">Enterprise-Grade Project Management</p>
            </div>
        </div>

        <!-- Auth Content -->
        <div class="container mx-auto px-4 py-12">
            <div class="grid md:grid-cols-2 gap-12 max-w-5xl mx-auto">
                <!-- Login Card -->
                <div class="bg-white rounded-2xl shadow-xl p-8">
                    <h2 class="section-title">Welcome Back</h2>
                    <p class="section-subtitle">Sign in to your account</p>
                    
                    <form id="loginForm">
                        <div class="form-group">
                            <label class="form-label">Email Address</label>
                            <input type="email" id="loginEmail" class="input-field" placeholder="<EMAIL>" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Password</label>
                            <input type="password" id="loginPassword" class="input-field" placeholder="••••••••" required>
                        </div>
                        <button type="submit" class="btn-primary w-full mb-4">Sign In</button>
                    </form>
                    
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm">
                        <p class="text-gray-700"><strong>Demo Account:</strong></p>
                        <p class="text-gray-600">Email: <EMAIL></p>
                        <p class="text-gray-600">Password: user123</p>
                    </div>
                </div>

                <!-- Register Card -->
                <div class="bg-white rounded-2xl shadow-xl p-8">
                    <h2 class="section-title">Create Account</h2>
                    <p class="section-subtitle">Join our team today</p>
                    
                    <form id="registerForm">
                        <div class="form-group">
                            <label class="form-label">Full Name</label>
                            <input type="text" id="registerName" class="input-field" placeholder="John Doe" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Email Address</label>
                            <input type="email" id="registerEmail" class="input-field" placeholder="<EMAIL>" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Password</label>
                            <input type="password" id="registerPassword" class="input-field" placeholder="••••••••" required>
                        </div>
                        <button type="submit" class="btn-primary w-full mb-4">Create Account</button>
                    </form>
                </div>
            </div>

            <!-- Features Section -->
            <div class="mt-20 max-w-5xl mx-auto">
                <div class="text-center mb-12">
                    <h3 class="text-3xl font-bold text-gray-800 mb-2">Powerful Features</h3>
                    <p class="text-gray-500">Everything you need to manage projects effectively</p>
                </div>
                
                <div class="grid md:grid-cols-3 gap-8">
                    <div class="bg-white rounded-xl shadow-md p-6 text-center hover:shadow-lg transition-shadow">
                        <div class="text-4xl mb-4">🔐</div>
                        <h4 class="font-bold text-gray-800 mb-2">Secure Auth</h4>
                        <p class="text-gray-600 text-sm">JWT-based authentication with refresh tokens</p>
                    </div>
                    <div class="bg-white rounded-xl shadow-md p-6 text-center hover:shadow-lg transition-shadow">
                        <div class="text-4xl mb-4">📊</div>
                        <h4 class="font-bold text-gray-800 mb-2">Analytics</h4>
                        <p class="text-gray-600 text-sm">Comprehensive reports and project insights</p>
                    </div>
                    <div class="bg-white rounded-xl shadow-md p-6 text-center hover:shadow-lg transition-shadow">
                        <div class="text-4xl mb-4">⚡</div>
                        <h4 class="font-bold text-gray-800 mb-2">Fast & Reliable</h4>
                        <p class="text-gray-600 text-sm">Built with FastAPI and PostgreSQL</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard -->
    <div id="dashboardPage" class="hidden min-h-screen bg-gray-50">
        <!-- Top Navbar -->
        <nav class="bg-white border-b border-gray-200 shadow-sm">
            <div class="container mx-auto px-6 py-4 flex justify-between items-center">
                <div class="flex items-center gap-3">
                    <div class="text-3xl">📋</div>
                    <h1 class="text-2xl font-bold text-gray-800">TaskTracker</h1>
                </div>
                <div class="flex items-center gap-6">
                    <div class="text-right">
                        <p class="text-sm text-gray-500">Logged in as</p>
                        <p class="font-semibold text-gray-800" id="navUserName"></p>
                    </div>
                    <button onclick="logout()" class="btn-danger py-2 px-4 text-sm">Logout</button>
                </div>
            </div>
        </nav>

        <div class="flex">
            <!-- Sidebar -->
            <aside style="width: 16rem; background-color: white; border-right: 1px solid #e5e7eb; min-height: 100vh;">
                <div style="padding: 1.5rem;">
                    <nav style="display: flex; flex-direction: column; gap: 0.5rem;">
                        <div class="sidebar-nav-item active" onclick="switchTab('dashboard')" style="background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%); color: white;">
                            <span style="font-size: 1.25rem;">📊</span>
                            <span>Dashboard</span>
                        </div>
                        <div class="sidebar-nav-item" onclick="switchTab('projects')">
                            <span class="text-xl">📁</span>
                            <span>Projects</span>
                        </div>
                        <div class="sidebar-nav-item" onclick="switchTab('tasks')">
                            <span class="text-xl">✓</span>
                            <span>Tasks</span>
                        </div>
                        <div id="adminNavItem" class="sidebar-nav-item hidden" onclick="switchTab('reports')">
                            <span class="text-xl">📈</span>
                            <span>Reports</span>
                        </div>
                        <div class="sidebar-nav-item" onclick="switchTab('profile')">
                            <span class="text-xl">👤</span>
                            <span>Profile</span>
                        </div>
                    </nav>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 p-8">
                <!-- Dashboard Tab -->
                <div id="dashboardTab" class="tab-content">
                    <div class="mb-8">
                        <h2 class="section-title">Dashboard</h2>
                        <p class="section-subtitle">Welcome back! Here's your overview</p>
                    </div>

                    <div class="grid md:grid-cols-4 gap-6 mb-8">
                        <div class="stat-card-gradient-blue">
                            <p style="color: rgba(255, 255, 255, 0.8); font-size: 0.875rem; font-weight: 500;">Total Projects</p>
                            <p style="font-size: 2.25rem; font-weight: 700; margin-top: 0.5rem;" id="dashTotalProjects">0</p>
                        </div>
                        <div class="stat-card-gradient-green">
                            <p style="color: rgba(255, 255, 255, 0.8); font-size: 0.875rem; font-weight: 500;">Total Tasks</p>
                            <p style="font-size: 2.25rem; font-weight: 700; margin-top: 0.5rem;" id="dashTotalTasks">0</p>
                        </div>
                        <div class="stat-card-gradient-yellow">
                            <p style="color: rgba(255, 255, 255, 0.8); font-size: 0.875rem; font-weight: 500;">In Progress</p>
                            <p style="font-size: 2.25rem; font-weight: 700; margin-top: 0.5rem;" id="dashInProgress">0</p>
                        </div>
                        <div class="stat-card-gradient-purple">
                            <p style="color: rgba(255, 255, 255, 0.8); font-size: 0.875rem; font-weight: 500;">Completed</p>
                            <p style="font-size: 2.25rem; font-weight: 700; margin-top: 0.5rem;" id="dashCompleted">0</p>
                        </div>
                    </div>

                    <div class="grid md:grid-cols-2 gap-8">
                        <!-- Recent Projects -->
                        <div class="stat-card">
                            <h3 class="font-bold text-gray-800 mb-4">📁 Recent Projects</h3>
                            <div id="dashRecentProjects" class="space-y-3">
                                <p class="text-gray-500">No projects yet</p>
                            </div>
                        </div>

                        <!-- Recent Tasks -->
                        <div class="stat-card">
                            <h3 class="font-bold text-gray-800 mb-4">✓ Recent Tasks</h3>
                            <div id="dashRecentTasks" class="space-y-3">
                                <p class="text-gray-500">No tasks yet</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Projects Tab -->
                <div id="projectsTab" class="tab-content hidden">
                    <div class="flex justify-between items-center mb-8">
                        <div>
                            <h2 class="section-title">Projects</h2>
                            <p class="section-subtitle">Manage and organize your projects</p>
                        </div>
                        <button onclick="openCreateProjectModal()" class="btn-primary">+ New Project</button>
                    </div>

                    <div id="projectsList" class="grid md:grid-cols-2 gap-6">
                        <p class="text-gray-500">Loading projects...</p>
                    </div>
                </div>

                <!-- Tasks Tab -->
                <div id="tasksTab" class="tab-content hidden">
                    <div class="flex justify-between items-center mb-8">
                        <div>
                            <h2 class="section-title">Tasks</h2>
                            <p class="section-subtitle">Create and manage your tasks</p>
                        </div>
                        <button onclick="openCreateTaskModal()" class="btn-primary">+ New Task</button>
                    </div>

                    <!-- Filters -->
                    <div class="bg-white rounded-xl shadow-md p-6 mb-6">
                        <div class="grid md:grid-cols-4 gap-4">
                            <div class="form-group mb-0">
                                <label class="form-label text-xs">Filter by Project</label>
                                <select id="filterProject" onchange="loadTasks()" class="input-field">
                                    <option value="">All Projects</option>
                                </select>
                            </div>
                            <div class="form-group mb-0">
                                <label class="form-label text-xs">Filter by Status</label>
                                <select id="filterStatus" onchange="loadTasks()" class="input-field">
                                    <option value="">All Status</option>
                                    <option value="PENDING">⏳ Pending</option>
                                    <option value="IN_PROGRESS">⚙️ In Progress</option>
                                    <option value="DONE">✅ Done</option>
                                </select>
                            </div>
                            <div class="form-group mb-0">
                                <label class="form-label text-xs">Filter by Priority</label>
                                <select id="filterPriority" onchange="loadTasks()" class="input-field">
                                    <option value="">All Priorities</option>
                                    <option value="LOW">🟢 Low</option>
                                    <option value="MEDIUM">🟡 Medium</option>
                                    <option value="HIGH">🔴 High</option>
                                </select>
                            </div>
                            <div class="form-group mb-0">
                                <label class="form-label text-xs">&nbsp;</label>
                                <button onclick="loadTasks()" class="btn-secondary w-full">Reset</button>
                            </div>
                        </div>
                    </div>

                    <div id="tasksList" class="space-y-4">
                        <p class="text-gray-500">Loading tasks...</p>
                    </div>
                </div>

                <!-- Reports Tab (Admin) -->
                <div id="reportsTab" class="tab-content hidden">
                    <div class="mb-8">
                        <h2 class="section-title">Admin Reports</h2>
                        <p class="section-subtitle">Project analytics and insights</p>
                    </div>

                    <div class="grid md:grid-cols-3 gap-6 mb-8">
                        <div class="stat-card-gradient-indigo">
                            <p style="color: rgba(255, 255, 255, 0.8); font-size: 0.875rem; font-weight: 500;">Completion Rate</p>
                            <p style="font-size: 2.25rem; font-weight: 700; margin-top: 0.5rem;" id="reportCompletion">0%</p>
                        </div>
                        <div class="stat-card-gradient-red">
                            <p style="color: rgba(255, 255, 255, 0.8); font-size: 0.875rem; font-weight: 500;">Overdue Tasks</p>
                            <p style="font-size: 2.25rem; font-weight: 700; margin-top: 0.5rem;" id="reportOverdue">0</p>
                        </div>
                        <div class="stat-card-gradient-cyan">
                            <p style="color: rgba(255, 255, 255, 0.8); font-size: 0.875rem; font-weight: 500;">Total Projects</p>
                            <p style="font-size: 2.25rem; font-weight: 700; margin-top: 0.5rem;" id="reportProjects">0</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <h3 class="font-bold text-gray-800 mb-6">Project Statistics</h3>
                        <div id="reportProjectStats" class="space-y-6">
                            <p class="text-gray-500">Loading statistics...</p>
                        </div>
                    </div>
                </div>

                <!-- Profile Tab -->
                <div id="profileTab" class="tab-content hidden">
                    <h2 class="section-title">My Profile</h2>
                    <p class="section-subtitle">View your account information</p>

                    <div class="stat-card max-w-2xl">
                        <div class="space-y-6">
                            <div class="pb-6 border-b border-gray-200">
                                <label class="form-label">Full Name</label>
                                <p id="profileName" class="text-gray-800 font-semibold">-</p>
                            </div>
                            <div class="pb-6 border-b border-gray-200">
                                <label class="form-label">Email Address</label>
                                <p id="profileEmail" class="text-gray-800 font-semibold">-</p>
                            </div>
                            <div class="pb-6 border-b border-gray-200">
                                <label class="form-label">Role</label>
                                <p id="profileRole" class="text-gray-800 font-semibold">
                                    <span id="profileRoleBadge" class="badge badge-primary">User</span>
                                </p>
                            </div>
                            <div>
                                <label class="form-label">Member Since</label>
                                <p id="profileCreated" class="text-gray-800 font-semibold">-</p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Create Project Modal -->
    <div id="createProjectModal" class="modal-overlay hidden" onclick="closeCreateProjectModal(event)">
        <div class="modal-content" onclick="event.stopPropagation()">
            <h3 class="section-title">Create New Project</h3>
            <form id="projectForm" class="mt-6">
                <div class="form-group">
                    <label class="form-label">Project Name</label>
                    <input type="text" id="projectName" class="input-field" placeholder="Enter project name" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Description</label>
                    <textarea id="projectDescription" class="input-field" placeholder="Enter project description" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">Start Date</label>
                    <input type="date" id="projectStartDate" class="input-field">
                </div>
                <div class="form-group">
                    <label class="form-label">End Date</label>
                    <input type="date" id="projectEndDate" class="input-field">
                </div>
                <div class="flex gap-3 mt-8">
                    <button type="submit" class="btn-primary flex-1">Create Project</button>
                    <button type="button" onclick="closeCreateProjectModal()" class="btn-secondary flex-1">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Create Task Modal -->
    <div id="createTaskModal" class="modal-overlay hidden" onclick="closeCreateTaskModal(event)">
        <div class="modal-content" onclick="event.stopPropagation()">
            <h3 class="section-title">Create New Task</h3>
            <form id="taskForm" class="mt-6">
                <div class="form-group">
                    <label class="form-label">Task Title</label>
                    <input type="text" id="taskTitle" class="input-field" placeholder="Enter task title" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Project</label>
                    <select id="taskProject" class="input-field" required>
                        <option value="">Select a project</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Description</label>
                    <textarea id="taskDescription" class="input-field" placeholder="Enter task description" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">Status</label>
                    <select id="taskStatus" class="input-field">
                        <option value="PENDING">⏳ Pending</option>
                        <option value="IN_PROGRESS">⚙️ In Progress</option>
                        <option value="DONE">✅ Done</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Priority</label>
                    <select id="taskPriority" class="input-field">
                        <option value="LOW">🟢 Low</option>
                        <option value="MEDIUM">🟡 Medium</option>
                        <option value="HIGH">🔴 High</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Due Date</label>
                    <input type="date" id="taskDueDate" class="input-field">
                </div>
                <div class="flex gap-3 mt-8">
                    <button type="submit" class="btn-primary flex-1">Create Task</button>
                    <button type="button" onclick="closeCreateTaskModal()" class="btn-secondary flex-1">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        const API_BASE_URL = window.location.origin;
        let authToken = localStorage.getItem('authToken');
        let currentUser = null;
        let projects = [];
        let tasks = [];
        let currentTab = 'dashboard';

        document.addEventListener('DOMContentLoaded', () => {
            if (authToken) {
                initializeDashboard();
            }

            document.getElementById('loginForm')?.addEventListener('submit', handleLogin);
            document.getElementById('registerForm')?.addEventListener('submit', handleRegister);
            document.getElementById('projectForm')?.addEventListener('submit', handleCreateProject);
            document.getElementById('taskForm')?.addEventListener('submit', handleCreateTask);
        });

        // Auth Handlers
        async function handleLogin(e) {
            e.preventDefault();
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            try {
                const formData = new FormData();
                formData.append('username', email);
                formData.append('password', password);
                
                const response = await axios.post(`${API_BASE_URL}/auth/login`, formData);
                authToken = response.data.access_token;
                localStorage.setItem('authToken', authToken);
                
                await initializeDashboard();
            } catch (error) {
                alert('Login failed: ' + (error.response?.data?.detail || 'Unknown error'));
            }
        }

        async function handleRegister(e) {
            e.preventDefault();
            const email = document.getElementById('registerEmail').value;
            const name = document.getElementById('registerName').value;
            const password = document.getElementById('registerPassword').value;
            
            try {
                await axios.post(`${API_BASE_URL}/auth/register`, {
                    email, name, password, role: 'user'
                });
                
                alert('Registration successful! Please login.');
                document.getElementById('registerForm').reset();
            } catch (error) {
                alert('Registration failed: ' + (error.response?.data?.detail || 'Unknown error'));
            }
        }

        async function initializeDashboard() {
            try {
                const response = await axios.get(`${API_BASE_URL}/auth/me`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                currentUser = response.data;
                
                document.getElementById('authPage').classList.add('hidden');
                document.getElementById('dashboardPage').classList.remove('hidden');
                
                document.getElementById('navUserName').textContent = currentUser.name;
                document.getElementById('profileName').textContent = currentUser.name;
                document.getElementById('profileEmail').textContent = currentUser.email;
                document.getElementById('profileRole').textContent = currentUser.role;
                
                const roleBadge = document.getElementById('profileRoleBadge');
                if (currentUser.role === 'admin') {
                    roleBadge.textContent = 'Admin';
                    roleBadge.className = 'badge badge-danger';
                    document.getElementById('adminNavItem').classList.remove('hidden');
                } else {
                    roleBadge.className = 'badge badge-primary';
                }
                
                document.getElementById('profileCreated').textContent = new Date(currentUser.created_at).toLocaleDateString();
                
                await loadProjects();
                await loadTasks();
                updateDashboard();
            } catch (error) {
                console.error('Failed to initialize:', error);
                logout();
            }
        }

        function logout() {
            localStorage.removeItem('authToken');
            authToken = null;
            currentUser = null;
            
            document.getElementById('authPage').classList.remove('hidden');
            document.getElementById('dashboardPage').classList.add('hidden');
            
            document.getElementById('loginForm').reset();
            document.getElementById('registerForm').reset();
        }

        // Tab Switching
        function switchTab(tabName) {
            currentTab = tabName;
            
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // Remove active from all nav items
            document.querySelectorAll('.sidebar-nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName + 'Tab').classList.remove('hidden');
            
            // Add active to the clicked nav item
            const clickedItem = event.currentTarget || event.target.closest('.sidebar-nav-item');
            if (clickedItem) {
                clickedItem.classList.add('active');
            }
            
            if (tabName === 'reports') {
                loadReports();
            }
        }

        // Projects
        async function loadProjects() {
            try {
                const response = await axios.get(`${API_BASE_URL}/projects/`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                projects = response.data;
                renderProjects();
                updateProjectFilters();
            } catch (error) {
                console.error('Failed to load projects:', error);
            }
        }

        function renderProjects() {
            const container = document.getElementById('projectsList');
            
            if (projects.length === 0) {
                container.innerHTML = '<p style="color: #6b7280; grid-column: 1 / -1;">No projects yet. Click "New Project" to create one!</p>';
                return;
            }
            
            container.innerHTML = projects.map(project => `
                <div class="project-card">
                    <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1rem;">
                        <div>
                            <h3 style="font-weight: 700; font-size: 1.125rem; color: #1f2937;">${project.name}</h3>
                            <p style="font-size: 0.875rem; color: #4b5563; margin-top: 0.25rem;">${project.description || 'No description'}</p>
                        </div>
                        <button onclick="deleteProject(${project.id})" style="color: #ef4444; cursor: pointer; border: none; background: none; font-size: 1.25rem;">🗑️</button>
                    </div>
                    <div style="padding-top: 1rem; border-top: 1px solid #e5e7eb;">
                        <div style="display: flex; justify-content: space-between; font-size: 0.75rem; color: #6b7280;">
                            ${project.start_date ? `<span>📅 ${new Date(project.start_date).toLocaleDateString()}</span>` : '<span></span>'}
                            ${project.end_date ? `<span>⏱️ ${new Date(project.end_date).toLocaleDateString()}</span>` : ''}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function updateProjectFilters() {
            const filterSelect = document.getElementById('filterProject');
            const taskSelect = document.getElementById('taskProject');
            
            const optionsHtml = projects.map(p => `<option value="${p.id}">${p.name}</option>`).join('');
            
            filterSelect.innerHTML = '<option value="">All Projects</option>' + optionsHtml;
            taskSelect.innerHTML = '<option value="">Select a project</option>' + optionsHtml;
        }

        async function handleCreateProject(e) {
            e.preventDefault();
            
            try {
                await axios.post(`${API_BASE_URL}/projects/`, {
                    name: document.getElementById('projectName').value,
                    description: document.getElementById('projectDescription').value,
                    start_date: document.getElementById('projectStartDate').value || null,
                    end_date: document.getElementById('projectEndDate').value || null,
                }, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                closeCreateProjectModal();
                await loadProjects();
                updateDashboard();
                alert('Project created successfully!');
            } catch (error) {
                alert('Failed to create project: ' + (error.response?.data?.detail || 'Unknown error'));
            }
        }

        async function deleteProject(projectId) {
            if (!confirm('Are you sure you want to delete this project?')) return;
            
            try {
                await axios.delete(`${API_BASE_URL}/projects/${projectId}`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                await loadProjects();
                updateDashboard();
                alert('Project deleted successfully!');
            } catch (error) {
                alert('Failed to delete project: ' + (error.response?.data?.detail || 'Unknown error'));
            }
        }

        function openCreateProjectModal() {
            const modal = document.getElementById('createProjectModal');
            modal.classList.remove('hidden');
            modal.style.display = 'flex';
        }

        function closeCreateProjectModal(event) {
            if (event && event.target !== document.getElementById('createProjectModal')) return;
            const modal = document.getElementById('createProjectModal');
            modal.classList.add('hidden');
            modal.style.display = 'none';
            document.getElementById('projectForm').reset();
        }

        // Tasks
        async function loadTasks() {
            try {
                let url = `${API_BASE_URL}/tasks/`;
                const params = new URLSearchParams();
                
                const projectId = document.getElementById('filterProject').value;
                const status = document.getElementById('filterStatus').value;
                const priority = document.getElementById('filterPriority').value;
                
                if (projectId) params.append('project_id', projectId);
                if (status) params.append('status', status);
                if (priority) params.append('priority', priority);
                
                if (params.toString()) url += '?' + params.toString();
                
                const response = await axios.get(url, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                tasks = response.data;
                renderTasks();
            } catch (error) {
                console.error('Failed to load tasks:', error);
            }
        }

        function renderTasks() {
            const container = document.getElementById('tasksList');
            
            if (tasks.length === 0) {
                container.innerHTML = '<p class="text-gray-500">No tasks found. Click "New Task" to create one!</p>';
                return;
            }
            
            container.innerHTML = tasks.map(task => {
                const priorityClass = {
                    'LOW': 'task-card-priority-low',
                    'MEDIUM': 'task-card-priority-medium',
                    'HIGH': 'task-card-priority-high'
                }[task.priority] || 'task-card-priority-low';
                
                const statusEmoji = { 'PENDING': '⏳', 'IN_PROGRESS': '⚙️', 'DONE': '✅' }[task.status];
                const priorityEmoji = { 'LOW': '🟢', 'MEDIUM': '🟡', 'HIGH': '🔴' }[task.priority];
                
                return `
                    <div class="task-card ${priorityClass}">
                        <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 0.75rem;">
                            <div style="flex: 1;">
                                <h4 style="font-weight: 700; color: #1f2937;">${task.title}</h4>
                                <p style="font-size: 0.875rem; color: #4b5563; margin-top: 0.25rem;">${task.description || 'No description'}</p>
                            </div>
                            <button onclick="deleteTask(${task.id})" style="color: #ef4444; cursor: pointer; border: none; background: none; font-size: 1.25rem;">🗑️</button>
                        </div>
                        
                        <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid #e5e7eb;">
                            <div style="display: flex; flex-wrap: wrap; gap: 0.75rem; align-items: center;">
                                <select onchange="updateTaskStatus(${task.id}, this.value)" style="padding: 0.25rem 0.75rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 0.875rem; cursor: pointer;">
                                    <option value="PENDING" ${task.status === 'PENDING' ? 'selected' : ''}>⏳ Pending</option>
                                    <option value="IN_PROGRESS" ${task.status === 'IN_PROGRESS' ? 'selected' : ''}>⚙️ In Progress</option>
                                    <option value="DONE" ${task.status === 'DONE' ? 'selected' : ''}>✅ Done</option>
                                </select>
                                
                                <span class="badge badge-primary" style="font-size: 0.75rem;">${priorityEmoji} ${task.priority}</span>
                                
                                ${task.due_date ? `<span style="font-size: 0.75rem; color: #6b7280;">📅 ${new Date(task.due_date).toLocaleDateString()}</span>` : ''}
                                
                                <span style="font-size: 0.75rem; color: #9ca3af; margin-left: auto;">${projects.find(p => p.id === task.project_id)?.name || 'Unknown'}</span>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        async function handleCreateTask(e) {
            e.preventDefault();
            
            try {
                await axios.post(`${API_BASE_URL}/tasks/`, {
                    title: document.getElementById('taskTitle').value,
                    description: document.getElementById('taskDescription').value,
                    project_id: parseInt(document.getElementById('taskProject').value),
                    status: document.getElementById('taskStatus').value,
                    priority: document.getElementById('taskPriority').value,
                    due_date: document.getElementById('taskDueDate').value || null,
                }, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                closeCreateTaskModal();
                await loadTasks();
                updateDashboard();
                alert('Task created successfully!');
            } catch (error) {
                alert('Failed to create task: ' + (error.response?.data?.detail || 'Unknown error'));
            }
        }

        async function updateTaskStatus(taskId, newStatus) {
            try {
                await axios.patch(`${API_BASE_URL}/tasks/${taskId}`, { status: newStatus }, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                await loadTasks();
                updateDashboard();
            } catch (error) {
                alert('Failed to update task: ' + (error.response?.data?.detail || 'Unknown error'));
            }
        }

        async function deleteTask(taskId) {
            if (!confirm('Are you sure you want to delete this task?')) return;
            
            try {
                await axios.delete(`${API_BASE_URL}/tasks/${taskId}`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                await loadTasks();
                updateDashboard();
                alert('Task deleted successfully!');
            } catch (error) {
                alert('Failed to delete task: ' + (error.response?.data?.detail || 'Unknown error'));
            }
        }

        function openCreateTaskModal() {
            const modal = document.getElementById('createTaskModal');
            modal.classList.remove('hidden');
            modal.style.display = 'flex';
        }

        function closeCreateTaskModal(event) {
            if (event && event.target !== document.getElementById('createTaskModal')) return;
            const modal = document.getElementById('createTaskModal');
            modal.classList.add('hidden');
            modal.style.display = 'none';
            document.getElementById('taskForm').reset();
        }

        // Dashboard
        function updateDashboard() {
            const totalProjects = projects.length;
            const totalTasks = tasks.length;
            const inProgressTasks = tasks.filter(t => t.status === 'IN_PROGRESS').length;
            const completedTasks = tasks.filter(t => t.status === 'DONE').length;
            
            document.getElementById('dashTotalProjects').textContent = totalProjects;
            document.getElementById('dashTotalTasks').textContent = totalTasks;
            document.getElementById('dashInProgress').textContent = inProgressTasks;
            document.getElementById('dashCompleted').textContent = completedTasks;
            
            const recentProjects = projects.slice(0, 5).map(p => 
                `<div class="text-sm"><strong>${p.name}</strong><br><span class="text-gray-500 text-xs">${p.description || 'No description'}</span></div>`
            ).join('');
            document.getElementById('dashRecentProjects').innerHTML = recentProjects || '<p class="text-gray-500">No projects</p>';
            
            const recentTasks = tasks.slice(0, 5).map(t => 
                `<div class="text-sm"><strong>${t.title}</strong><br><span class="text-gray-500 text-xs">${t.status === 'DONE' ? '✅ Done' : t.status === 'IN_PROGRESS' ? '⚙️ In Progress' : '⏳ Pending'}</span></div>`
            ).join('');
            document.getElementById('dashRecentTasks').innerHTML = recentTasks || '<p class="text-gray-500">No tasks</p>';
        }

        // Reports
        async function loadReports() {
            try {
                const [completionRes, overdueRes, projectsRes] = await Promise.all([
                    axios.get(`${API_BASE_URL}/reports/completion`, {
                        headers: { 'Authorization': `Bearer ${authToken}` }
                    }),
                    axios.get(`${API_BASE_URL}/reports/overdue`, {
                        headers: { 'Authorization': `Bearer ${authToken}` }
                    }),
                    axios.get(`${API_BASE_URL}/reports/projects`, {
                        headers: { 'Authorization': `Bearer ${authToken}` }
                    })
                ]);
                
                const completionRate = Math.round(completionRes.data.completion_rate * 100);
                document.getElementById('reportCompletion').textContent = completionRate + '%';
                document.getElementById('reportOverdue').textContent = overdueRes.data.overdue_count;
                document.getElementById('reportProjects').textContent = projectsRes.data.length;
                
                const statsHtml = projectsRes.data.map(project => {
                    const rate = Math.round(project.completion_rate * 100);
                    return `
                        <div class="pb-6 border-b border-gray-200 last:border-b-0">
                            <div class="flex justify-between items-center mb-2">
                                <h4 class="font-semibold text-gray-800">${project.name}</h4>
                                <span class="text-sm text-gray-600">${rate}%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${rate}%"></div>
                            </div>
                            <p class="text-sm text-gray-500 mt-2">${project.completed_tasks}/${project.total_tasks} tasks completed</p>
                        </div>
                    `;
                }).join('');
                
                document.getElementById('reportProjectStats').innerHTML = statsHtml;
            } catch (error) {
                console.error('Failed to load reports:', error);
            }
        }
    </script>
</body>
</html>
