run = "uvicorn app.main:app --host 0.0.0.0 --port 8000"
language = "python3"

[env]
DATABASE_URL = "sqlite:///./task_tracker.db"
SECRET_KEY = "replit-secret-key-change-in-production"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = "30"
REFRESH_TOKEN_EXPIRE_DAYS = "7"

[packager]
language = "python3"

[packager.features]
packageSearch = true
guessImports = true
enabledForHosting = false

[[ports]]
localPort = 8000
externalPort = 80