#!/usr/bin/env python3
"""Basic test to verify the application structure."""

import sys
import os

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

def test_imports():
    """Test that all main modules can be imported."""
    try:
        from app.main import app
        from app.core.config import settings
        from app.core.database import Base, get_db
        from app.core.security import get_password_hash, verify_password
        from app.models.user import User, UserRole
        from app.models.project import Project
        from app.models.task import Task, TaskStatus, TaskPriority
        from app.schemas.user import UserCreate, UserResponse
        from app.schemas.project import ProjectCreate, ProjectResponse
        from app.schemas.task import TaskCreate, TaskResponse
        from app.services.auth_service import AuthService
        from app.services.user_service import UserService
        from app.services.project_service import ProjectService
        from app.services.task_service import TaskService
        
        print("✅ All imports successful!")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_password_hashing():
    """Test password hashing functionality."""
    try:
        from app.core.security import get_password_hash, verify_password
        
        password = "testpassword123"
        hashed = get_password_hash(password)
        
        assert verify_password(password, hashed), "Password verification failed"
        assert not verify_password("wrongpassword", hashed), "Password verification should fail for wrong password"
        
        print("✅ Password hashing works!")
        return True
    except Exception as e:
        print(f"❌ Password hashing error: {e}")
        return False

def test_config():
    """Test configuration loading."""
    try:
        from app.core.config import settings
        
        assert settings.app_name == "Task & Resource Tracker API"
        assert settings.algorithm == "HS256"
        assert settings.access_token_expire_minutes > 0
        
        print("✅ Configuration loaded successfully!")
        return True
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def main():
    """Run all basic tests."""
    print("Running basic application tests...")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_password_hashing,
        test_config
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The application is ready to run.")
        print("\nTo start the application:")
        print("uvicorn app.main:app --reload")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)