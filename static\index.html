<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task & Resource Tracker</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/1.6.2/axios.min.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div id="app" class="min-h-screen">
        <!-- Navbar -->
        <nav id="navbar" class="bg-blue-600 text-white p-4 hidden">
            <div class="container mx-auto flex justify-between items-center">
                <h1 class="text-2xl font-bold">📋 Task & Resource Tracker</h1>
                <div class="flex items-center gap-4">
                    <span id="userDisplayName" class="text-sm"></span>
                    <button onclick="logout()" class="bg-red-500 px-4 py-2 rounded hover:bg-red-600">Logout</button>
                </div>
            </div>
        </nav>

        <!-- Main Container -->
        <div class="container mx-auto px-4 py-8">
            <!-- Auth Section (shown before login) -->
            <div id="authSection" class="max-w-4xl mx-auto">
                <header class="text-center mb-8">
                    <h1 class="text-4xl font-bold text-gray-800 mb-2">Task & Resource Tracker</h1>
                    <p class="text-gray-600">Production-ready FastAPI Backend Demo</p>
                </header>

                <div class="max-w-4xl mx-auto">
                    <!-- Authentication Section -->
                    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                        <h2 class="text-2xl font-semibold mb-4">Authentication</h2>
                        
                        <div class="grid md:grid-cols-2 gap-6">
                            <!-- Login -->
                            <div class="border rounded-lg p-4">
                                <h3 class="text-lg font-medium mb-3">Login</h3>
                                <form id="loginForm">
                                    <div class="mb-3">
                                        <input type="email" id="loginEmail" placeholder="Email" 
                                               class="w-full px-3 py-2 border rounded-md" required>
                                    </div>
                                    <div class="mb-3">
                                        <input type="password" id="loginPassword" placeholder="Password" 
                                               class="w-full px-3 py-2 border rounded-md" required>
                                    </div>
                                    <button type="submit" class="w-full bg-blue-500 text-white py-2 rounded-md hover:bg-blue-600">
                                        Login
                                    </button>
                                </form>
                                <p class="text-xs text-gray-500 mt-2">Demo: <EMAIL> / user123</p>
                            </div>

                            <!-- Register -->
                            <div class="border rounded-lg p-4">
                                <h3 class="text-lg font-medium mb-3">Register</h3>
                                <form id="registerForm">
                                    <div class="mb-3">
                                        <input type="email" id="registerEmail" placeholder="Email" 
                                               class="w-full px-3 py-2 border rounded-md" required>
                                    </div>
                                    <div class="mb-3">
                                        <input type="text" id="registerName" placeholder="Name" 
                                               class="w-full px-3 py-2 border rounded-md" required>
                                    </div>
                                    <div class="mb-3">
                                        <input type="password" id="registerPassword" placeholder="Password" 
                                               class="w-full px-3 py-2 border rounded-md" required>
                                    </div>
                                    <button type="submit" class="w-full bg-green-500 text-white py-2 rounded-md hover:bg-green-600">
                                        Register
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- API Endpoints -->
                    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                        <h2 class="text-2xl font-semibold mb-4">API Endpoints</h2>
                        <div class="grid md:grid-cols-2 gap-4">
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h3 class="font-medium text-blue-800 mb-2">Authentication</h3>
                                <ul class="text-sm text-blue-600 space-y-1">
                                    <li>POST /auth/register</li>
                                    <li>POST /auth/login</li>
                                    <li>GET /auth/me</li>
                                    <li>POST /auth/refresh</li>
                                </ul>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <h3 class="font-medium text-green-800 mb-2">Tasks</h3>
                                <ul class="text-sm text-green-600 space-y-1">
                                    <li>POST /tasks/</li>
                                    <li>GET /tasks/</li>
                                    <li>GET /tasks/{id}</li>
                                    <li>PATCH /tasks/{id}</li>
                                    <li>DELETE /tasks/{id}</li>
                                </ul>
                            </div>
                            <div class="bg-purple-50 p-4 rounded-lg">
                                <h3 class="font-medium text-purple-800 mb-2">Projects</h3>
                                <ul class="text-sm text-purple-600 space-y-1">
                                    <li>POST /projects/</li>
                                    <li>GET /projects/</li>
                                    <li>GET /projects/{id}</li>
                                    <li>PUT /projects/{id}</li>
                                    <li>DELETE /projects/{id}</li>
                                </ul>
                            </div>
                            <div class="bg-orange-50 p-4 rounded-lg">
                                <h3 class="font-medium text-orange-800 mb-2">Reports (Admin)</h3>
                                <ul class="text-sm text-orange-600 space-y-1">
                                    <li>GET /reports/completion</li>
                                    <li>GET /reports/overdue</li>
                                    <li>GET /reports/projects</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Dashboard Section (shown after login) -->
            <div id="dashboardSection" class="hidden">
                <!-- Tabs -->
                <div class="flex gap-4 mb-6 border-b">
                    <button onclick="showTab('profile')" class="tab-btn py-2 px-4 border-b-2 border-blue-600">👤 Profile</button>
                    <button onclick="showTab('projects')" class="tab-btn py-2 px-4 border-b-2 border-transparent hover:border-gray-300">📁 Projects</button>
                    <button onclick="showTab('tasks')" class="tab-btn py-2 px-4 border-b-2 border-transparent hover:border-gray-300">✓ Tasks</button>
                    <button id="reportsTab" onclick="showTab('reports')" class="tab-btn py-2 px-4 border-b-2 border-transparent hover:border-gray-300 hidden">📊 Reports</button>
                </div>

                <!-- Profile Tab -->
                <div id="profileTab" class="tab-content">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h2 class="text-2xl font-semibold mb-4">👤 Your Profile</h2>
                        <div id="profileDetails" class="bg-gray-50 p-4 rounded-lg">
                            <p><strong>Name:</strong> <span id="profileName"></span></p>
                            <p><strong>Email:</strong> <span id="profileEmail"></span></p>
                            <p><strong>Role:</strong> <span id="profileRole"></span></p>
                            <p><strong>Member since:</strong> <span id="profileCreated"></span></p>
                        </div>
                    </div>
                </div>

                <!-- Projects Tab -->
                <div id="projectsTab" class="tab-content hidden">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h2 class="text-2xl font-semibold">📁 Projects</h2>
                            <button onclick="showCreateProjectForm()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">+ New Project</button>
                        </div>

                        <!-- Create Project Form -->
                        <div id="createProjectForm" class="bg-gray-50 p-4 rounded-lg mb-6 hidden">
                            <h3 class="font-semibold mb-4">Create New Project</h3>
                            <form id="projectForm">
                                <div class="grid md:grid-cols-2 gap-4 mb-4">
                                    <input type="text" id="projectName" placeholder="Project Name" class="px-3 py-2 border rounded-md" required>
                                    <input type="text" id="projectDescription" placeholder="Description" class="px-3 py-2 border rounded-md">
                                </div>
                                <div class="grid md:grid-cols-2 gap-4 mb-4">
                                    <input type="date" id="projectStartDate" class="px-3 py-2 border rounded-md">
                                    <input type="date" id="projectEndDate" class="px-3 py-2 border rounded-md">
                                </div>
                                <div class="flex gap-2">
                                    <button type="submit" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">Create Project</button>
                                    <button type="button" onclick="hideCreateProjectForm()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">Cancel</button>
                                </div>
                            </form>
                        </div>

                        <!-- Projects List -->
                        <div id="projectsList" class="grid gap-4">
                            <!-- Projects will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Tasks Tab -->
                <div id="tasksTab" class="tab-content hidden">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h2 class="text-2xl font-semibold">✓ Tasks</h2>
                            <button onclick="showCreateTaskForm()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">+ New Task</button>
                        </div>

                        <!-- Filters -->
                        <div class="bg-gray-50 p-4 rounded-lg mb-6">
                            <div class="grid md:grid-cols-3 gap-4">
                                <select id="filterProject" onchange="loadTasks()" class="px-3 py-2 border rounded-md">
                                    <option value="">All Projects</option>
                                </select>
                                <select id="filterStatus" onchange="loadTasks()" class="px-3 py-2 border rounded-md">
                                    <option value="">All Status</option>
                                    <option value="PENDING">⏳ Pending</option>
                                    <option value="IN_PROGRESS">⚙️ In Progress</option>
                                    <option value="DONE">✅ Done</option>
                                </select>
                                <select id="filterPriority" onchange="loadTasks()" class="px-3 py-2 border rounded-md">
                                    <option value="">All Priorities</option>
                                    <option value="LOW">🟢 Low</option>
                                    <option value="MEDIUM">🟡 Medium</option>
                                    <option value="HIGH">🔴 High</option>
                                </select>
                            </div>
                        </div>

                        <!-- Create Task Form -->
                        <div id="createTaskForm" class="bg-gray-50 p-4 rounded-lg mb-6 hidden">
                            <h3 class="font-semibold mb-4">Create New Task</h3>
                            <form id="taskForm">
                                <div class="grid md:grid-cols-2 gap-4 mb-4">
                                    <input type="text" id="taskTitle" placeholder="Task Title" class="px-3 py-2 border rounded-md" required>
                                    <select id="taskProject" class="px-3 py-2 border rounded-md" required>
                                        <option value="">Select Project</option>
                                    </select>
                                </div>
                                <div class="mb-4">
                                    <textarea id="taskDescription" placeholder="Description" class="w-full px-3 py-2 border rounded-md" rows="3"></textarea>
                                </div>
                                <div class="grid md:grid-cols-3 gap-4 mb-4">
                                    <select id="taskStatus" class="px-3 py-2 border rounded-md">
                                        <option value="PENDING">⏳ Pending</option>
                                        <option value="IN_PROGRESS">⚙️ In Progress</option>
                                        <option value="DONE">✅ Done</option>
                                    </select>
                                    <select id="taskPriority" class="px-3 py-2 border rounded-md">
                                        <option value="LOW">🟢 Low</option>
                                        <option value="MEDIUM">🟡 Medium</option>
                                        <option value="HIGH">🔴 High</option>
                                    </select>
                                    <input type="date" id="taskDueDate" class="px-3 py-2 border rounded-md">
                                </div>
                                <div class="flex gap-2">
                                    <button type="submit" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">Create Task</button>
                                    <button type="button" onclick="hideCreateTaskForm()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">Cancel</button>
                                </div>
                            </form>
                        </div>

                        <!-- Tasks List -->
                        <div id="tasksList" class="grid gap-4">
                            <!-- Tasks will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Reports Tab (Admin only) -->
                <div id="reportsTab" class="tab-content hidden">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h2 class="text-2xl font-semibold mb-6">📊 Admin Reports</h2>
                        
                        <div class="grid md:grid-cols-3 gap-6 mb-8">
                            <div class="bg-blue-50 p-6 rounded-lg">
                                <h3 class="font-semibold text-blue-800 mb-2">Overall Completion</h3>
                                <p id="completionRate" class="text-3xl font-bold text-blue-600">0%</p>
                            </div>
                            <div class="bg-orange-50 p-6 rounded-lg">
                                <h3 class="font-semibold text-orange-800 mb-2">Overdue Tasks</h3>
                                <p id="overdueCount" class="text-3xl font-bold text-orange-600">0</p>
                            </div>
                            <div class="bg-green-50 p-6 rounded-lg">
                                <h3 class="font-semibold text-green-800 mb-2">Total Projects</h3>
                                <p id="totalProjects" class="text-3xl font-bold text-green-600">0</p>
                            </div>
                        </div>

                        <!-- Project Stats -->
                        <div class="mb-8">
                            <h3 class="text-xl font-semibold mb-4">Project Statistics</h3>
                            <div id="projectStats" class="grid gap-4">
                                <!-- Stats will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = window.location.origin;
        let authToken = localStorage.getItem('authToken');
        let currentUser = null;
        let projects = [];
        let tasks = [];

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            if (authToken) {
                loadUserProfile();
            }

            // Setup event listeners
            document.getElementById('loginForm')?.addEventListener('submit', handleLogin);
            document.getElementById('registerForm')?.addEventListener('submit', handleRegister);
            document.getElementById('projectForm')?.addEventListener('submit', handleCreateProject);
            document.getElementById('taskForm')?.addEventListener('submit', handleCreateTask);
        });

        // Auth handlers
        async function handleLogin(e) {
            e.preventDefault();
            
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            try {
                const formData = new FormData();
                formData.append('username', email);
                formData.append('password', password);
                
                const response = await axios.post(`${API_BASE_URL}/auth/login`, formData);
                
                authToken = response.data.access_token;
                localStorage.setItem('authToken', authToken);
                
                await loadUserProfile();
                showDashboard();
                alert('Login successful!');
            } catch (error) {
                alert('Login failed: ' + (error.response?.data?.detail || 'Unknown error'));
            }
        }

        async function handleRegister(e) {
            e.preventDefault();
            
            const email = document.getElementById('registerEmail').value;
            const name = document.getElementById('registerName').value;
            const password = document.getElementById('registerPassword').value;
            
            try {
                await axios.post(`${API_BASE_URL}/auth/register`, {
                    email, name, password, role: 'user'
                });
                
                alert('Registration successful! Please login.');
                document.getElementById('registerForm').reset();
            } catch (error) {
                alert('Registration failed: ' + (error.response?.data?.detail || 'Unknown error'));
            }
        }

        async function loadUserProfile() {
            try {
                const response = await axios.get(`${API_BASE_URL}/auth/me`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                currentUser = response.data;
                
                // Update navbar
                document.getElementById('userDisplayName').textContent = currentUser.name;
                
                // Update profile tab
                document.getElementById('profileName').textContent = currentUser.name;
                document.getElementById('profileEmail').textContent = currentUser.email;
                document.getElementById('profileRole').textContent = currentUser.role;
                document.getElementById('profileCreated').textContent = new Date(currentUser.created_at).toLocaleDateString();
                
                // Show admin report tab if admin
                if (currentUser.role === 'admin') {
                    document.getElementById('reportsTab').classList.remove('hidden');
                }
                
                await loadProjects();
                await loadTasks();
            } catch (error) {
                localStorage.removeItem('authToken');
                authToken = null;
                alert('Session expired. Please login again.');
            }
        }

        function showDashboard() {
            document.getElementById('authSection').classList.add('hidden');
            document.getElementById('dashboardSection').classList.remove('hidden');
            document.getElementById('navbar').classList.remove('hidden');
        }

        function logout() {
            localStorage.removeItem('authToken');
            authToken = null;
            currentUser = null;
            
            document.getElementById('authSection').classList.remove('hidden');
            document.getElementById('dashboardSection').classList.add('hidden');
            document.getElementById('navbar').classList.add('hidden');
            
            // Reset forms
            document.getElementById('loginForm').reset();
            document.getElementById('registerForm').reset();
        }

        // Tab functions
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // Remove active border from all buttons
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('border-blue-600');
                btn.classList.add('border-transparent');
            });
            
            // Show selected tab
            document.getElementById(tabName + 'Tab').classList.remove('hidden');
            
            // Add active border to clicked button
            event.target.classList.add('border-blue-600');
            event.target.classList.remove('border-transparent');
            
            // Load reports if admin tab
            if (tabName === 'reports' && currentUser?.role === 'admin') {
                loadReports();
            }
        }

        // Projects functions
        async function loadProjects() {
            try {
                const response = await axios.get(`${API_BASE_URL}/projects/`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                projects = response.data;
                renderProjects();
                updateProjectFilters();
            } catch (error) {
                console.error('Failed to load projects:', error);
            }
        }

        function renderProjects() {
            const container = document.getElementById('projectsList');
            
            if (projects.length === 0) {
                container.innerHTML = '<p class="text-gray-500">No projects yet. Create one to get started!</p>';
                return;
            }
            
            container.innerHTML = projects.map(project => `
                <div class="bg-gray-50 p-4 rounded-lg border-l-4 border-purple-500">
                    <div class="flex justify-between items-start mb-2">
                        <div>
                            <h3 class="font-semibold text-lg">${project.name}</h3>
                            <p class="text-sm text-gray-600">${project.description || 'No description'}</p>
                        </div>
                        <button onclick="deleteProject(${project.id})" class="text-red-500 hover:text-red-700">🗑️</button>
                    </div>
                    <div class="text-xs text-gray-500 mt-2">
                        ${project.start_date ? `Start: ${new Date(project.start_date).toLocaleDateString()}` : ''} |
                        ${project.end_date ? `End: ${new Date(project.end_date).toLocaleDateString()}` : 'No end date'}
                    </div>
                </div>
            `).join('');
        }

        function updateProjectFilters() {
            const select = document.getElementById('filterProject');
            const taskSelect = document.getElementById('taskProject');
            
            select.innerHTML = '<option value="">All Projects</option>' + 
                projects.map(p => `<option value="${p.id}">${p.name}</option>`).join('');
            
            taskSelect.innerHTML = '<option value="">Select Project</option>' + 
                projects.map(p => `<option value="${p.id}">${p.name}</option>`).join('');
        }

        function showCreateProjectForm() {
            document.getElementById('createProjectForm').classList.remove('hidden');
        }

        function hideCreateProjectForm() {
            document.getElementById('createProjectForm').classList.add('hidden');
            document.getElementById('projectForm').reset();
        }

        async function handleCreateProject(e) {
            e.preventDefault();
            
            try {
                await axios.post(`${API_BASE_URL}/projects/`, {
                    name: document.getElementById('projectName').value,
                    description: document.getElementById('projectDescription').value,
                    start_date: document.getElementById('projectStartDate').value || null,
                    end_date: document.getElementById('projectEndDate').value || null,
                }, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                hideCreateProjectForm();
                await loadProjects();
                alert('Project created successfully!');
            } catch (error) {
                alert('Failed to create project: ' + (error.response?.data?.detail || 'Unknown error'));
            }
        }

        async function deleteProject(projectId) {
            if (!confirm('Are you sure you want to delete this project?')) return;
            
            try {
                await axios.delete(`${API_BASE_URL}/projects/${projectId}`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                await loadProjects();
                alert('Project deleted successfully!');
            } catch (error) {
                alert('Failed to delete project: ' + (error.response?.data?.detail || 'Unknown error'));
            }
        }

        // Tasks functions
        async function loadTasks() {
            try {
                let url = `${API_BASE_URL}/tasks/`;
                const params = new URLSearchParams();
                
                const projectId = document.getElementById('filterProject').value;
                const status = document.getElementById('filterStatus').value;
                const priority = document.getElementById('filterPriority').value;
                
                if (projectId) params.append('project_id', projectId);
                if (status) params.append('status', status);
                if (priority) params.append('priority', priority);
                
                if (params.toString()) url += '?' + params.toString();
                
                const response = await axios.get(url, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                tasks = response.data;
                renderTasks();
            } catch (error) {
                console.error('Failed to load tasks:', error);
            }
        }

        function renderTasks() {
            const container = document.getElementById('tasksList');
            
            if (tasks.length === 0) {
                container.innerHTML = '<p class="text-gray-500">No tasks found. Create one to get started!</p>';
                return;
            }
            
            container.innerHTML = tasks.map(task => {
                const statusEmoji = {
                    'PENDING': '⏳',
                    'IN_PROGRESS': '⚙️',
                    'DONE': '✅'
                }[task.status] || '•';
                
                const priorityEmoji = {
                    'LOW': '🟢',
                    'MEDIUM': '🟡',
                    'HIGH': '🔴'
                }[task.priority] || '•';
                
                return `
                    <div class="bg-gray-50 p-4 rounded-lg border-l-4 border-green-500">
                        <div class="flex justify-between items-start mb-2">
                            <div class="flex-1">
                                <h3 class="font-semibold text-lg">${task.title}</h3>
                                <p class="text-sm text-gray-600">${task.description || 'No description'}</p>
                            </div>
                            <button onclick="deleteTask(${task.id})" class="text-red-500 hover:text-red-700">🗑️</button>
                        </div>
                        <div class="flex gap-4 items-center text-sm">
                            <select class="px-2 py-1 border rounded" onchange="updateTaskStatus(${task.id}, this.value)">
                                <option value="PENDING" ${task.status === 'PENDING' ? 'selected' : ''}>⏳ Pending</option>
                                <option value="IN_PROGRESS" ${task.status === 'IN_PROGRESS' ? 'selected' : ''}>⚙️ In Progress</option>
                                <option value="DONE" ${task.status === 'DONE' ? 'selected' : ''}>✅ Done</option>
                            </select>
                            <span class="text-gray-500">${priorityEmoji} ${task.priority}</span>
                            ${task.due_date ? `<span class="text-gray-500">📅 ${new Date(task.due_date).toLocaleDateString()}</span>` : ''}
                            <span class="text-gray-500 text-xs">Project: ${projects.find(p => p.id === task.project_id)?.name || 'Unknown'}</span>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function showCreateTaskForm() {
            document.getElementById('createTaskForm').classList.remove('hidden');
        }

        function hideCreateTaskForm() {
            document.getElementById('createTaskForm').classList.add('hidden');
            document.getElementById('taskForm').reset();
        }

        async function handleCreateTask(e) {
            e.preventDefault();
            
            try {
                await axios.post(`${API_BASE_URL}/tasks/`, {
                    title: document.getElementById('taskTitle').value,
                    description: document.getElementById('taskDescription').value,
                    project_id: parseInt(document.getElementById('taskProject').value),
                    status: document.getElementById('taskStatus').value,
                    priority: document.getElementById('taskPriority').value,
                    due_date: document.getElementById('taskDueDate').value || null,
                }, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                hideCreateTaskForm();
                await loadTasks();
                alert('Task created successfully!');
            } catch (error) {
                alert('Failed to create task: ' + (error.response?.data?.detail || 'Unknown error'));
            }
        }

        async function updateTaskStatus(taskId, newStatus) {
            try {
                await axios.patch(`${API_BASE_URL}/tasks/${taskId}`, {
                    status: newStatus
                }, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                await loadTasks();
            } catch (error) {
                alert('Failed to update task: ' + (error.response?.data?.detail || 'Unknown error'));
            }
        }

        async function deleteTask(taskId) {
            if (!confirm('Are you sure you want to delete this task?')) return;
            
            try {
                await axios.delete(`${API_BASE_URL}/tasks/${taskId}`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                
                await loadTasks();
                alert('Task deleted successfully!');
            } catch (error) {
                alert('Failed to delete task: ' + (error.response?.data?.detail || 'Unknown error'));
            }
        }

        // Reports functions
        async function loadReports() {
            try {
                const [completionRes, overdueRes, projectsRes] = await Promise.all([
                    axios.get(`${API_BASE_URL}/reports/completion`, {
                        headers: { 'Authorization': `Bearer ${authToken}` }
                    }),
                    axios.get(`${API_BASE_URL}/reports/overdue`, {
                        headers: { 'Authorization': `Bearer ${authToken}` }
                    }),
                    axios.get(`${API_BASE_URL}/reports/projects`, {
                        headers: { 'Authorization': `Bearer ${authToken}` }
                    })
                ]);
                
                // Update completion rate
                const completionRate = Math.round(completionRes.data.completion_rate * 100);
                document.getElementById('completionRate').textContent = completionRate + '%';
                
                // Update overdue count
                document.getElementById('overdueCount').textContent = overdueRes.data.overdue_count;
                
                // Update project stats
                document.getElementById('totalProjects').textContent = projectsRes.data.length;
                
                const statsContainer = document.getElementById('projectStats');
                statsContainer.innerHTML = projectsRes.data.map(project => `
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="font-semibold">${project.name}</div>
                        <div class="text-sm text-gray-600 mt-2">
                            <p>Total Tasks: ${project.total_tasks}</p>
                            <p>Completed: ${project.completed_tasks} (${Math.round(project.completion_rate * 100)}%)</p>
                        </div>
                    </div>
                `).join('');
                
            } catch (error) {
                console.error('Failed to load reports:', error);
            }
        }
    </script>
</body>
</html>