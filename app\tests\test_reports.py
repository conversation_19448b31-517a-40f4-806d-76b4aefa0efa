"""Reporting tests."""

import pytest
from datetime import date, timedelta


class TestReports:
    """Test reporting endpoints."""
    
    def test_get_completion_stats_admin(self, client, admin_auth_headers):
        """Test getting completion stats as admin."""
        response = client.get("/reports/completion", headers=admin_auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert "total" in data
        assert "completed" in data
        assert "pending" in data
        assert "in_progress" in data
        assert "completion_rate" in data
    
    def test_get_completion_stats_non_admin(self, client, auth_headers):
        """Test getting completion stats as non-admin."""
        response = client.get("/reports/completion", headers=auth_headers)
        assert response.status_code == 403
        assert "Admin access required" in response.json()["detail"]
    
    def test_get_overdue_tasks_admin(self, client, admin_auth_headers):
        """Test getting overdue tasks as admin."""
        response = client.get("/reports/overdue", headers=admin_auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_get_project_stats_admin(self, client, admin_auth_headers):
        """Test getting project stats as admin."""
        response = client.get("/reports/projects", headers=admin_auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert "total_projects" in data
        assert "active_projects" in data
        assert "completed_projects" in data
        assert "projects_with_tasks" in data
    
    def test_reports_unauthorized(self, client):
        """Test accessing reports without authentication."""
        endpoints = ["/reports/completion", "/reports/overdue", "/reports/projects"]
        
        for endpoint in endpoints:
            response = client.get(endpoint)
            assert response.status_code == 401