"""Test configuration and fixtures."""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.core.database import Base, get_db
from app.core.security import get_password_hash
from app.main import app
from app.models.user import User, UserRole
from app.models.project import Project
from app.models.task import Task, TaskStatus, TaskPriority


# Create test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override database dependency for testing."""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


@pytest.fixture(scope="function")
def test_db():
    """Create test database for each test."""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def client(test_db):
    """Create test client."""
    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as test_client:
        yield test_client
    app.dependency_overrides.clear()


@pytest.fixture
def test_user():
    """Create test user."""
    db = TestingSessionLocal()
    user = User(
        email="<EMAIL>",
        name="Test User",
        password_hash=get_password_hash("testpassword123"),
        role=UserRole.USER
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    yield user
    db.close()


@pytest.fixture
def test_admin():
    """Create test admin user."""
    db = TestingSessionLocal()
    user = User(
        email="<EMAIL>",
        name="Admin User",
        password_hash=get_password_hash("adminpassword123"),
        role=UserRole.ADMIN
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    yield user
    db.close()


@pytest.fixture
def test_project(test_user):
    """Create test project."""
    db = TestingSessionLocal()
    project = Project(
        name="Test Project",
        description="A test project",
        owner_id=test_user.id
    )
    db.add(project)
    db.commit()
    db.refresh(project)
    yield project
    db.close()


@pytest.fixture
def test_task(test_user, test_project):
    """Create test task."""
    db = TestingSessionLocal()
    task = Task(
        title="Test Task",
        description="A test task",
        status=TaskStatus.PENDING,
        priority=TaskPriority.MEDIUM,
        assigned_to_id=test_user.id,
        project_id=test_project.id
    )
    db.add(task)
    db.commit()
    db.refresh(task)
    yield task
    db.close()


@pytest.fixture
def auth_headers(test_user):
    """Get authentication headers for test user."""
    from app.services.auth_service import AuthService
    from app.schemas.auth import LoginRequest
    
    db = TestingSessionLocal()
    login_request = LoginRequest(email="<EMAIL>", password="testpassword123")
    user = AuthService.authenticate_user(db, login_request)
    
    if user:
        access_token, _ = AuthService.create_tokens(user)
        return {"Authorization": f"Bearer {access_token}"}
    
    return {}


@pytest.fixture
def admin_auth_headers(test_admin):
    """Get authentication headers for test admin."""
    from app.services.auth_service import AuthService
    from app.schemas.auth import LoginRequest
    
    db = TestingSessionLocal()
    login_request = LoginRequest(email="<EMAIL>", password="adminpassword123")
    user = AuthService.authenticate_user(db, login_request)
    
    if user:
        access_token, _ = AuthService.create_tokens(user)
        return {"Authorization": f"Bearer {access_token}"}
    
    return {}