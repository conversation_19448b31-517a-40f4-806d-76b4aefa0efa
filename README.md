# Task & Resource Tracker API

A production-ready FastAPI backend for task and resource management, built with clean architecture principles and modern Python practices.

## 🚀 Features

- **Authentication & Authorization**: JWT-based authentication with role-based access control
- **Task Management**: Full CRUD operations with filtering and search
- **Project Organization**: Group tasks into projects with ownership
- **User Management**: User registration, profiles, and admin controls
- **Reporting**: Completion statistics and overdue task reports
- **API Documentation**: Auto-generated Swagger and ReDoc documentation
- **Testing**: Comprehensive test suite with pytest
- **Database**: PostgreSQL with SQLAlchemy ORM and Alembic migrations

## 🛠 Tech Stack

| Component | Technology |
|-----------|------------|
| Framework | FastAPI |
| Language | Python 3.11+ |
| Database | PostgreSQL |
| ORM | SQLAlchemy + Alembic |
| Authentication | JWT (python-jose) |
| Password Hashing | bcrypt |
| Validation | Pydantic |
| Testing | pytest + HTTPX |
| Deployment | Docker + Docker Compose |

## 📁 Project Structure

```
app/
├── main.py                 # Application entry point
├── core/                   # Core functionality
│   ├── config.py          # Configuration management
│   ├── database.py        # Database connection
│   └── security.py        # Security utilities
├── models/                 # SQLAlchemy models
│   ├── user.py
│   ├── project.py
│   └── task.py
├── schemas/                # Pydantic schemas
│   ├── auth.py
│   ├── user.py
│   ├── project.py
│   └── task.py
├── services/               # Business logic
│   ├── auth_service.py
│   ├── user_service.py
│   ├── project_service.py
│   └── task_service.py
├── routers/                # API endpoints
│   ├── auth.py
│   ├── users.py
│   ├── projects.py
│   ├── tasks.py
│   └── reports.py
└── tests/                  # Test suite
    ├── conftest.py
    ├── test_auth.py
    ├── test_users.py
    ├── test_projects.py
    ├── test_tasks.py
    └── test_reports.py
```

## 🚦 Getting Started

### Prerequisites

- Python 3.11+
- PostgreSQL 15+
- Docker (optional)

### Local Development

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd task-tracker
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up database**
   ```bash
   # Create database
   createdb task_tracker
   
   # Run migrations
   alembic upgrade head
   ```

5. **Run the application**
   ```bash
   uvicorn app.main:app --reload
   ```

   The API will be available at `http://localhost:8000`
   - Swagger UI: `http://localhost:8000/docs`
   - ReDoc: `http://localhost:8000/redoc`

### Docker Development

1. **Build and run with Docker Compose**
   ```bash
   docker-compose up --build
   ```

2. **Run migrations**
   ```bash
   docker-compose exec app alembic upgrade head
   ```

## 🔐 Authentication

The API uses JWT-based authentication:

1. **Register a new user**
   ```bash
   POST /auth/register
   {
     "email": "<EMAIL>",
     "name": "John Doe",
     "password": "securepassword123"
   }
   ```

2. **Login**
   ```bash
   POST /auth/login
   {
     "username": "<EMAIL>",
     "password": "securepassword123"
   }
   ```

3. **Use the token**
   Include the access token in the Authorization header:
   ```
   Authorization: Bearer <access_token>
   ```

## 📚 API Endpoints

### Authentication
- `POST /auth/register` - Register new user
- `POST /auth/login` - User login
- `GET /auth/me` - Get current user
- `POST /auth/refresh` - Refresh access token

### Users
- `GET /users/me` - Get current user profile
- `PUT /users/me` - Update current user profile
- `GET /users/{id}` - Get user by ID (admin only)
- `GET /users/` - List all users (admin only)

### Projects
- `POST /projects/` - Create new project
- `GET /projects/` - List user's projects
- `GET /projects/{id}` - Get project by ID
- `PUT /projects/{id}` - Update project
- `DELETE /projects/{id}` - Delete project

### Tasks
- `POST /tasks/` - Create new task
- `GET /tasks/` - List tasks with filtering
- `GET /tasks/{id}` - Get task by ID
- `PUT /tasks/{id}` - Update task
- `DELETE /tasks/{id}` - Delete task

### Reports (Admin Only)
- `GET /reports/completion` - Task completion statistics
- `GET /reports/overdue` - Overdue tasks
- `GET /reports/projects` - Project statistics

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app --cov-report=html

# Run specific test file
pytest app/tests/test_auth.py
```

## 🚀 Deployment

### Render Deployment

1. Create a new Web Service on Render
2. Connect your GitHub repository
3. Set environment variables in Render dashboard
4. Deploy!

### Railway Deployment

1. Connect your GitHub repository to Railway
2. Add PostgreSQL database service
3. Configure environment variables
4. Deploy!

### Environment Variables

Required environment variables:
- `DATABASE_URL` - PostgreSQL connection string
- `SECRET_KEY` - Secret key for JWT signing
- `ALGORITHM` - JWT algorithm (default: HS256)

## 🔧 Development

### Code Style

The project uses:
- **Black** for code formatting
- **isort** for import sorting
- **flake8** for linting
- **mypy** for type checking

Run code quality tools:
```bash
# Format code
black app/
isort app/

# Lint
flake8 app/

# Type check
mypy app/
```

### Database Migrations

Create a new migration:
```bash
alembic revision --autogenerate -m "description"
```

Apply migrations:
```bash
alembic upgrade head
```

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 🔮 Future Enhancements

- [ ] Team collaboration features
- [ ] Real-time notifications
- [ ] File attachments
- [ ] Time tracking
- [ ] Advanced reporting and analytics
- [ ] Third-party integrations
- [ ] Mobile API endpoints
- [ ] WebSocket support for real-time updates