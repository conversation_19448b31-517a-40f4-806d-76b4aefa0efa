"""User management tests."""

import pytest


class TestUsers:
    """Test user management endpoints."""
    
    def test_get_me(self, client, test_user, auth_headers):
        """Test getting current user profile."""
        response = client.get("/users/me", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["email"] == test_user.email
        assert data["name"] == test_user.name
    
    def test_update_me(self, client, test_user, auth_headers):
        """Test updating current user profile."""
        update_data = {
            "name": "Updated Name",
            "email": "<EMAIL>"
        }
        
        response = client.put("/users/me", json=update_data, headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == update_data["name"]
        assert data["email"] == update_data["email"]
    
    def test_update_me_partial(self, client, test_user, auth_headers):
        """Test partial update of current user profile."""
        update_data = {"name": "Partial Update"}
        
        response = client.put("/users/me", json=update_data, headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == update_data["name"]
        assert data["email"] == test_user.email  # Should remain unchanged
    
    def test_get_user_by_id_admin(self, client, test_user, admin_auth_headers):
        """Test getting user by ID as admin."""
        response = client.get(f"/users/{test_user.id}", headers=admin_auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["email"] == test_user.email
        assert data["name"] == test_user.name
    
    def test_get_user_by_id_non_admin(self, client, test_user, auth_headers):
        """Test getting user by ID as non-admin."""
        response = client.get(f"/users/{test_user.id}", headers=auth_headers)
        assert response.status_code == 403
        assert "Admin access required" in response.json()["detail"]
    
    def test_get_users_admin(self, client, admin_auth_headers):
        """Test getting all users as admin."""
        response = client.get("/users/", headers=admin_auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
    
    def test_get_users_non_admin(self, client, auth_headers):
        """Test getting all users as non-admin."""
        response = client.get("/users/", headers=auth_headers)
        assert response.status_code == 403
        assert "Admin access required" in response.json()["detail"]