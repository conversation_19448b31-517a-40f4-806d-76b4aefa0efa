"""Task management tests."""

import pytest
from datetime import date, timedelta


class TestTasks:
    """Test task management endpoints."""
    
    def test_create_task(self, client, auth_headers):
        """Test creating a new task."""
        task_data = {
            "title": "Test Task",
            "description": "A test task description",
            "status": "pending",
            "priority": "high",
            "due_date": str(date.today() + timedelta(days=7))
        }
        
        response = client.post("/tasks/", json=task_data, headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == task_data["title"]
        assert data["description"] == task_data["description"]
        assert data["status"] == task_data["status"]
        assert data["priority"] == task_data["priority"]
        assert "id" in data
    
    def test_create_task_with_project(self, client, test_project, auth_headers):
        """Test creating a task assigned to a project."""
        task_data = {
            "title": "Project Task",
            "description": "Task for project",
            "project_id": test_project.id
        }
        
        response = client.post("/tasks/", json=task_data, headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["project_id"] == test_project.id
    
    def test_get_tasks(self, client, test_task, auth_headers):
        """Test getting tasks."""
        response = client.get("/tasks/", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
        assert data[0]["title"] == test_task.title
    
    def test_get_tasks_filtered_by_status(self, client, test_task, auth_headers):
        """Test getting tasks filtered by status."""
        response = client.get("/tasks/?status=pending", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert all(task["status"] == "pending" for task in data)
    
    def test_get_tasks_filtered_by_priority(self, client, test_task, auth_headers):
        """Test getting tasks filtered by priority."""
        response = client.get("/tasks/?priority=medium", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert all(task["priority"] == "medium" for task in data)
    
    def test_get_tasks_search(self, client, test_task, auth_headers):
        """Test searching tasks."""
        response = client.get("/tasks/?search=test", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert len(data) >= 1
        assert "test" in data[0]["title"].lower()
    
    def test_get_task_by_id(self, client, test_task, auth_headers):
        """Test getting task by ID."""
        response = client.get(f"/tasks/{test_task.id}", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_task.id
        assert data["title"] == test_task.title
    
    def test_get_task_unauthorized(self, client, test_task):
        """Test getting task without authentication."""
        response = client.get(f"/tasks/{test_task.id}")
        assert response.status_code == 401
    
    def test_update_task(self, client, test_task, auth_headers):
        """Test updating task."""
        update_data = {
            "title": "Updated Task Title",
            "status": "in_progress",
            "priority": "high"
        }
        
        response = client.put(f"/tasks/{test_task.id}", json=update_data, headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == update_data["title"]
        assert data["status"] == update_data["status"]
        assert data["priority"] == update_data["priority"]
    
    def test_update_task_partial(self, client, test_task, auth_headers):
        """Test partial update of task."""
        update_data = {"status": "done"}
        
        response = client.put(f"/tasks/{test_task.id}", json=update_data, headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "done"
        assert data["title"] == test_task.title  # Should remain unchanged
    
    def test_delete_task(self, client, test_task, auth_headers):
        """Test deleting task."""
        response = client.delete(f"/tasks/{test_task.id}", headers=auth_headers)
        assert response.status_code == 204
        
        # Verify task is deleted
        get_response = client.get(f"/tasks/{test_task.id}", headers=auth_headers)
        assert get_response.status_code == 404
    
    def test_delete_task_unauthorized(self, client, test_task):
        """Test deleting task without authentication."""
        response = client.delete(f"/tasks/{test_task.id}")
        assert response.status_code == 401