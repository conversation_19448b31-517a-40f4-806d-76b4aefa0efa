"""Project management tests."""

import pytest


class TestProjects:
    """Test project management endpoints."""
    
    def test_create_project(self, client, auth_headers):
        """Test creating a new project."""
        project_data = {
            "name": "Test Project",
            "description": "A test project description",
            "start_date": "2025-01-01",
            "end_date": "2025-12-31"
        }
        
        response = client.post("/projects/", json=project_data, headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == project_data["name"]
        assert data["description"] == project_data["description"]
        assert "id" in data
        assert "owner_id" in data
    
    def test_get_projects(self, client, test_project, auth_headers):
        """Test getting user's projects."""
        response = client.get("/projects/", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1
        assert data[0]["name"] == test_project.name
    
    def test_get_project_by_id(self, client, test_project, auth_headers):
        """Test getting project by ID."""
        response = client.get(f"/projects/{test_project.id}", headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_project.id
        assert data["name"] == test_project.name
    
    def test_get_project_unauthorized(self, client, test_project):
        """Test getting project without authentication."""
        response = client.get(f"/projects/{test_project.id}")
        assert response.status_code == 401
    
    def test_get_project_other_user(self, client, test_project):
        """Test getting project owned by another user."""
        # Create another user
        user_data = {
            "email": "<EMAIL>",
            "name": "Other User",
            "password": "password123"
        }
        
        register_response = client.post("/auth/register", json=user_data)
        other_user_id = register_response.json()["id"]
        
        # Login as other user
        login_response = client.post("/auth/login", data={
            "username": "<EMAIL>",
            "password": "password123"
        })
        other_auth_headers = {"Authorization": f"Bearer {login_response.json()['access_token']}"}
        
        # Try to access project owned by test_user
        response = client.get(f"/projects/{test_project.id}", headers=other_auth_headers)
        assert response.status_code == 403
    
    def test_update_project(self, client, test_project, auth_headers):
        """Test updating project."""
        update_data = {
            "name": "Updated Project Name",
            "description": "Updated description"
        }
        
        response = client.put(f"/projects/{test_project.id}", json=update_data, headers=auth_headers)
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == update_data["name"]
        assert data["description"] == update_data["description"]
    
    def test_update_project_unauthorized(self, client, test_project):
        """Test updating project without authentication."""
        update_data = {"name": "Updated Name"}
        response = client.put(f"/projects/{test_project.id}", json=update_data)
        assert response.status_code == 401
    
    def test_delete_project(self, client, test_project, auth_headers):
        """Test deleting project."""
        response = client.delete(f"/projects/{test_project.id}", headers=auth_headers)
        assert response.status_code == 204
        
        # Verify project is deleted
        get_response = client.get(f"/projects/{test_project.id}", headers=auth_headers)
        assert get_response.status_code == 404
    
    def test_delete_project_unauthorized(self, client, test_project):
        """Test deleting project without authentication."""
        response = client.delete(f"/projects/{test_project.id}")
        assert response.status_code == 401